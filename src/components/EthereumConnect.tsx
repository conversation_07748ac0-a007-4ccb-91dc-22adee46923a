"use client";
import { useState, useEffect } from "react";
import { useAccount, useConnect, useDisconnect } from "wagmi";

export default function EthereumConnect() {
  const { address, isConnected } = useAccount();
  const { connect, connectors, isPending } = useConnect();
  const { disconnect } = useDisconnect();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) return null;

  return (
    <div className="border rounded-lg p-4 bg-blue-50">
      <h3 className="text-lg font-semibold mb-2">Ethereum Wallet</h3>

      {isConnected ? (
        <div className="flex flex-col items-center space-y-2">
          <p className="text-sm text-gray-600">
            Connected to Ethereum: {address?.slice(0, 6)}...{address?.slice(-4)}
          </p>
          <button
            onClick={() => disconnect()}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          >
            Disconnect Ethereum
          </button>
        </div>
      ) : (
        <div className="flex flex-col items-center space-y-2">
          <p className="text-sm text-gray-600">Connect your Ethereum wallet</p>
          <div className="flex space-x-2">
            {connectors.map((connector) => (
              <button
                key={connector.uid}
                onClick={() => connect({ connector })}
                disabled={isPending}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400 transition-colors"
              >
                {connector.name}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
