import { EndpointId } from "@layerzerolabs/lz-definitions";
import { Network } from "../components/NetworkSelector";

export interface BridgeConfig {
  fromNetwork: Network;
  toNetwork: Network;
  fromEndpointId: number;
  toEndpointId: number;
  fromContractAddress?: string;
  toContractAddress?: string;
  bridgeType: 'SOLANA_TO_EVM' | 'EVM_TO_SOLANA' | 'EVM_TO_EVM';
  isSupported: boolean;
}

export const ENDPOINT_IDS = {
  solana: EndpointId.SOLANA_V2_MAINNET,
  ethereum: EndpointId.ETHEREUM_V2_MAINNET,
  base: EndpointId.BASE_V2_MAINNET,
} as const;

export const CONTRACT_ADDRESSES = {
  ethereum: process.env.NEXT_PUBLIC_ETHEREUM_OFT_ADDRESS,
  base: process.env.NEXT_PUBLIC_BASE_OFT_ADDRESS,
  solana: {
    mint: process.env.NEXT_PUBLIC_SOLANA_OFT_MINT_ADDRESS,
    escrow: process.env.NEXT_PUBLIC_SOLANA_ESCROW_ADDRESS,
    program: process.env.NEXT_PUBLIC_SOLANA_PROGRAM_ADDRESS,
    store: process.env.NEXT_PUBLIC_SOLANA_OFT_STORE_ADDRESS,
  }
} as const;

export function getBridgeConfig(fromNetwork: Network, toNetwork: Network): BridgeConfig {
  const fromEndpointId = ENDPOINT_IDS[fromNetwork.id as keyof typeof ENDPOINT_IDS];
  const toEndpointId = ENDPOINT_IDS[toNetwork.id as keyof typeof ENDPOINT_IDS];
  
  let bridgeType: BridgeConfig['bridgeType'];
  let isSupported = true;
  let fromContractAddress: string | undefined;
  let toContractAddress: string | undefined;

  if (fromNetwork.id === 'solana' && (toNetwork.id === 'ethereum' || toNetwork.id === 'base')) {
    bridgeType = 'SOLANA_TO_EVM';
    toContractAddress = CONTRACT_ADDRESSES[toNetwork.id as 'ethereum' | 'base'];
  } else if ((fromNetwork.id === 'ethereum' || fromNetwork.id === 'base') && toNetwork.id === 'solana') {
    bridgeType = 'EVM_TO_SOLANA';
    fromContractAddress = CONTRACT_ADDRESSES[fromNetwork.id as 'ethereum' | 'base'];
  } else if ((fromNetwork.id === 'ethereum' || fromNetwork.id === 'base') && 
             (toNetwork.id === 'ethereum' || toNetwork.id === 'base') && 
             fromNetwork.id !== toNetwork.id) {
    bridgeType = 'EVM_TO_EVM';
    fromContractAddress = CONTRACT_ADDRESSES[fromNetwork.id as 'ethereum' | 'base'];
    toContractAddress = CONTRACT_ADDRESSES[toNetwork.id as 'ethereum' | 'base'];
  } else {
    // Invalid combination (same network or unsupported)
    bridgeType = 'EVM_TO_EVM';
    isSupported = false;
  }

  return {
    fromNetwork,
    toNetwork,
    fromEndpointId,
    toEndpointId,
    fromContractAddress,
    toContractAddress,
    bridgeType,
    isSupported,
  };
}

export function getNetworkRpcUrl(networkId: string): string {
  switch (networkId) {
    case 'solana':
      return process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com";
    case 'ethereum':
      return process.env.NEXT_PUBLIC_ETHEREUM_RPC_URL || "https://eth.llamarpc.com/";
    case 'base':
      return process.env.NEXT_PUBLIC_BASE_RPC_URL || "https://mainnet.base.org";
    default:
      throw new Error(`Unsupported network: ${networkId}`);
  }
}

export function getChainId(networkId: string): number | undefined {
  switch (networkId) {
    case 'ethereum':
      return 1;
    case 'base':
      return 8453;
    case 'solana':
      return undefined; // Solana doesn't use chain IDs
    default:
      throw new Error(`Unsupported network: ${networkId}`);
  }
}

export const TOKEN_DECIMALS = {
  solana: 6,
  ethereum: 18,
  base: 18,
} as const;

export function getTokenDecimals(networkId: string): number {
  return TOKEN_DECIMALS[networkId as keyof typeof TOKEN_DECIMALS] || 18;
}
